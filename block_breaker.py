import pygame
import sys
import random

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
PADDLE_WIDTH = 100
PADDLE_HEIGHT = 15
BALL_SIZE = 15
BRICK_WIDTH = 75
BRICK_HEIGHT = 30
BRICK_ROWS = 6
BRICK_COLS = 10
PADDLE_SPEED = 8
BALL_SPEED = 5

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
BLUE = (0, 100, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
YELLOW = (255, 255, 0)
ORANGE = (255, 165, 0)
PURPLE = (128, 0, 128)

class Paddle:
    def __init__(self, x, y):
        self.rect = pygame.Rect(x, y, PADDLE_WIDTH, PADDLE_HEIGHT)
        self.speed = PADDLE_SPEED
    
    def move_left(self):
        if self.rect.left > 0:
            self.rect.x -= self.speed
    
    def move_right(self):
        if self.rect.right < SCREEN_WIDTH:
            self.rect.x += self.speed
    
    def draw(self, screen):
        pygame.draw.rect(screen, BLUE, self.rect)

class Ball:
    def __init__(self, x, y):
        self.rect = pygame.Rect(x, y, BALL_SIZE, BALL_SIZE)
        self.speed_x = BALL_SPEED
        self.speed_y = -BALL_SPEED
    
    def move(self):
        self.rect.x += self.speed_x
        self.rect.y += self.speed_y
    
    def bounce_x(self):
        self.speed_x = -self.speed_x
    
    def bounce_y(self):
        self.speed_y = -self.speed_y
    
    def draw(self, screen):
        pygame.draw.ellipse(screen, WHITE, self.rect)

class Brick:
    def __init__(self, x, y, color):
        self.rect = pygame.Rect(x, y, BRICK_WIDTH, BRICK_HEIGHT)
        self.color = color
        self.destroyed = False
    
    def draw(self, screen):
        if not self.destroyed:
            pygame.draw.rect(screen, self.color, self.rect)
            pygame.draw.rect(screen, BLACK, self.rect, 2)

class Game:
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Block Breaker")
        self.clock = pygame.time.Clock()
        
        # Initialize game objects
        self.paddle = Paddle(SCREEN_WIDTH // 2 - PADDLE_WIDTH // 2, SCREEN_HEIGHT - 50)
        self.ball = Ball(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
        self.bricks = []
        self.score = 0
        self.lives = 3
        self.game_over = False
        self.won = False
        
        # Create bricks
        self.create_bricks()
        
        # Font for text
        self.font = pygame.font.Font(None, 36)
    
    def create_bricks(self):
        colors = [RED, ORANGE, YELLOW, GREEN, BLUE, PURPLE]
        brick_start_x = (SCREEN_WIDTH - (BRICK_COLS * BRICK_WIDTH)) // 2
        brick_start_y = 50
        
        for row in range(BRICK_ROWS):
            for col in range(BRICK_COLS):
                x = brick_start_x + col * BRICK_WIDTH
                y = brick_start_y + row * BRICK_HEIGHT
                color = colors[row % len(colors)]
                self.bricks.append(Brick(x, y, color))
    
    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_r and (self.game_over or self.won):
                    self.restart_game()
        return True
    
    def handle_input(self):
        keys = pygame.key.get_pressed()
        mouse_pos = pygame.mouse.get_pos()
        
        # Keyboard controls
        if keys[pygame.K_LEFT] or keys[pygame.K_a]:
            self.paddle.move_left()
        if keys[pygame.K_RIGHT] or keys[pygame.K_d]:
            self.paddle.move_right()
        
        # Mouse controls
        self.paddle.rect.centerx = mouse_pos[0]
        if self.paddle.rect.left < 0:
            self.paddle.rect.left = 0
        if self.paddle.rect.right > SCREEN_WIDTH:
            self.paddle.rect.right = SCREEN_WIDTH
    
    def update(self):
        if self.game_over or self.won:
            return
        
        # Move ball
        self.ball.move()
        
        # Ball collision with walls
        if self.ball.rect.left <= 0 or self.ball.rect.right >= SCREEN_WIDTH:
            self.ball.bounce_x()
        if self.ball.rect.top <= 0:
            self.ball.bounce_y()
        
        # Ball falls off screen
        if self.ball.rect.bottom >= SCREEN_HEIGHT:
            self.lives -= 1
            if self.lives <= 0:
                self.game_over = True
            else:
                # Reset ball position
                self.ball.rect.center = (SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
                self.ball.speed_x = BALL_SPEED if random.choice([True, False]) else -BALL_SPEED
                self.ball.speed_y = -BALL_SPEED
        
        # Ball collision with paddle
        if self.ball.rect.colliderect(self.paddle.rect):
            # Calculate bounce angle based on where ball hits paddle
            hit_pos = (self.ball.rect.centerx - self.paddle.rect.centerx) / (PADDLE_WIDTH / 2)
            self.ball.speed_x = BALL_SPEED * hit_pos
            self.ball.speed_y = -abs(self.ball.speed_y)
        
        # Ball collision with bricks
        for brick in self.bricks:
            if not brick.destroyed and self.ball.rect.colliderect(brick.rect):
                brick.destroyed = True
                self.score += 10
                self.ball.bounce_y()
                break
        
        # Check win condition
        if all(brick.destroyed for brick in self.bricks):
            self.won = True
    
    def draw(self):
        self.screen.fill(BLACK)
        
        # Draw game objects
        self.paddle.draw(self.screen)
        self.ball.draw(self.screen)
        
        for brick in self.bricks:
            brick.draw(self.screen)
        
        # Draw UI
        score_text = self.font.render(f"Score: {self.score}", True, WHITE)
        lives_text = self.font.render(f"Lives: {self.lives}", True, WHITE)
        self.screen.blit(score_text, (10, 10))
        self.screen.blit(lives_text, (10, 50))
        
        # Draw game over or win message
        if self.game_over:
            game_over_text = self.font.render("GAME OVER! Press R to restart", True, RED)
            text_rect = game_over_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
            self.screen.blit(game_over_text, text_rect)
        elif self.won:
            win_text = self.font.render("YOU WIN! Press R to restart", True, GREEN)
            text_rect = win_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
            self.screen.blit(win_text, text_rect)
        
        pygame.display.flip()
    
    def restart_game(self):
        self.__init__()
    
    def run(self):
        running = True
        while running:
            running = self.handle_events()
            self.handle_input()
            self.update()
            self.draw()
            self.clock.tick(60)
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = Game()
    game.run()
